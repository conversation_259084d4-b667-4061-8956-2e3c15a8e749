# Installation
> `npm install --save @types/png-chunk-text`

# Summary
This package contains type definitions for png-chunk-text (https://github.com/hughsk/png-chunk-text).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/png-chunk-text.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/png-chunk-text/index.d.ts)
````ts
export function decode(data: Uint8Array): { keyword: string; text: string };

export function encode(keyword: string, text: string): { name: "tEXt"; data: Uint8Array };

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/phaux).
