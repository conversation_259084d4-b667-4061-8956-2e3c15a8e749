# Installation
> `npm install --save @types/png-chunks-extract`

# Summary
This package contains type definitions for png-chunks-extract (https://github.com/hughsk/png-chunks-extract).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/png-chunks-extract.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/png-chunks-extract/index.d.ts)
````ts
declare function extractChunks(data: Uint8Array): Array<{ name: string; data: Uint8Array }>;

export = extractChunks;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/phaux).
