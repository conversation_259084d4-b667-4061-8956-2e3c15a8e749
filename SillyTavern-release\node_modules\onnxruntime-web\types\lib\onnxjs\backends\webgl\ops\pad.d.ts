import { AttributeWith<PERSON>ache<PERSON>ey } from '../../../attribute-with-cache-key';
import { OperatorImplementation, OperatorInitialization } from '../../../operators';
export interface PadAttributes extends AttributeWithCacheKey {
    readonly mode: string;
    readonly pads: number[];
    readonly value: number;
}
export declare const padV2: OperatorImplementation<PadAttributes>;
export declare const parsePadAttributesV2: OperatorInitialization<PadAttributes>;
export declare const padV11: OperatorImplementation<string>;
export declare const parsePadAttributesV11: OperatorInitialization<string>;
