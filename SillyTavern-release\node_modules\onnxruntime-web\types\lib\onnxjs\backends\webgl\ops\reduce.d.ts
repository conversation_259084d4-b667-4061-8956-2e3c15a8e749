import { Attribute<PERSON>ith<PERSON>ache<PERSON>ey } from '../../../attribute-with-cache-key';
import { OperatorImplementation, OperatorInitialization } from '../../../operators';
export interface ReduceAttributes extends AttributeWith<PERSON>acheKey {
    readonly axes: number[];
    readonly keepDims: boolean;
}
export declare const parseReduceAttributes: OperatorInitialization<ReduceAttributes>;
export declare const reduceSum: OperatorImplementation<ReduceAttributes>;
export declare const reduceMean: OperatorImplementation<ReduceAttributes>;
export declare const reduceMax: OperatorImplementation<ReduceAttributes>;
export declare const reduceMin: OperatorImplementation<ReduceAttributes>;
export declare const reduceProd: OperatorImplementation<ReduceAttributes>;
export declare const reduceLogSum: OperatorImplementation<ReduceAttributes>;
export declare const reduceLogSumSquare: OperatorImplementation<ReduceAttributes>;
