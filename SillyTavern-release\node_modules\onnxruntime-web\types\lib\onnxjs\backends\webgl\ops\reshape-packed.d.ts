import { Tensor } from '../../../tensor';
import { WebGLInferenceHandler } from '../inference-handler';
import { ProgramInfoLoader } from '../types';
export declare const createPackedReshape3DProgramInfoLoader: (handler: WebGLInferenceHandler, input3D: Tensor, outputShape3D: readonly number[]) => ProgramInfoLoader;
export declare function processDims3D(shape: ArrayLike<number>): [number, number, number];
export declare function isReshapeCheap(dims: readonly number[], reshapedDims: readonly number[]): boolean;
