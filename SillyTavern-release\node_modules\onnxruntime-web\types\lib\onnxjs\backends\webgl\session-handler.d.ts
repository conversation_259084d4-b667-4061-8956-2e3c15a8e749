import { <PERSON><PERSON><PERSON><PERSON> } from '../../backend';
import { Graph } from '../../graph';
import { Operator } from '../../operators';
import { OpSet } from '../../opset';
import { Session } from '../../session';
import { Tensor } from '../../tensor';
import { WebGLBackend } from '../backend-webgl';
import { WebGLInferenceHandler } from './inference-handler';
import { ProgramManager } from './program-manager';
import { TextureLayoutStrategy } from './texture-layout-strategy';
import { TextureManager } from './texture-manager';
import { TextureData } from './types';
export declare class WebGLSessionHandler implements SessionHandler {
    readonly backend: WebGLBackend;
    readonly context: Session.Context;
    programManager: ProgramManager;
    textureManager: TextureManager;
    layoutStrategy: TextureLayoutStrategy;
    packedTextureDataCache: Map<Tensor.Id, TextureData>;
    unpackedTextureDataCache: Map<Tensor.Id, TextureData>;
    pack2unpackMap: Map<Tensor.Id, Tensor.Id>;
    unpack2packMap: Map<Tensor.Id, Tensor.Id>;
    initializers: Set<Tensor.Id>;
    pack?: boolean;
    constructor(backend: WebGLBackend, context: Session.Context);
    createInferenceHandler(): WebGLInferenceHandler;
    onGraphInitialized(graph: Graph): void;
    isInitializer(tensorId: Tensor.Id): boolean;
    addInitializer(tensorId: Tensor.Id): void;
    getTextureData(tensorId: Tensor.Id, isPacked: boolean): TextureData | undefined;
    setTextureData(tensorId: Tensor.Id, textureData: TextureData, isPacked?: boolean): void;
    dispose(): void;
    resolve(node: Graph.Node, opsets: readonly OpSet[], graph: Graph): Operator;
}
