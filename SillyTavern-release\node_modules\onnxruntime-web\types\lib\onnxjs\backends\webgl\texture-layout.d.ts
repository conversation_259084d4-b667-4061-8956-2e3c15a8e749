import { TextureLayoutStrategy, WidthHeightPrefs } from './texture-layout-strategy';
import { TextureLayout, TextureType } from './types';
export declare const createTextureLayoutFromTextureType: (textureLayoutStrategy: TextureLayoutStrategy, shape: readonly number[], textureType: TextureType) => TextureLayout;
export declare const calculateTextureWidthAndHeight: (textureLayoutStrategy: TextureLayoutStrategy, shape: readonly number[], textureType: TextureType) => [
    number,
    number
];
/**
 * Create a TextureLayout object from shape.
 */
export declare const createTextureLayoutFromShape: (textureLayoutStrategy: TextureLayoutStrategy, shape: readonly number[], channels?: 1 | 4, unpackedShape?: readonly number[], prefs?: WidthHeightPrefs) => TextureLayout;
