import { WebGLContext } from './webgl-context';
/**
 * This factory function creates proper WebGLRenderingContext based on
 * the current browsers capabilities
 * The order is from higher/most recent versions to most basic
 */
export declare function createWebGLContext(contextId?: 'webgl' | 'webgl2'): WebGLContext;
export declare function createNewWebGLContext(contextId?: 'webgl' | 'webgl2'): WebGLContext;
